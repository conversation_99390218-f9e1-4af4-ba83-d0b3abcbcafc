package com.ymx.service.photovoltaic.station.mapper;

import com.ymx.service.photovoltaic.station.model.ComponentDayModel;
import com.ymx.service.photovoltaic.station.model.ComponentElectricModel;
import com.ymx.service.photovoltaic.station.model.ComponentHourModel;
import com.ymx.service.photovoltaic.station.model.PowerStationModel;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Repository
public interface ComponentDayMapper {

	/**
	 * 批量新增统计数据
	 * @param map
	 * @return
	 */
	int saveComponentDayList(Map<String, Object> map);
	
	/**
	 * 根据批次号查询功率
	 * @param map
	 * @return
	 */
	List<ComponentHourModel> queryGroupPowerByBatchNo(Map<String, Object> map);
	
	List<ComponentDayModel> queryComponentByYear(Map<String, Object> map);
	List<ComponentDayModel> queryComponentByMonth(Map<String, Object> map);
	List<ComponentDayModel> queryComponentByDay(Map<String, Object> map);
	// 获取分组电流 电压数据
	List<ComponentHourModel> queryDataByGroupId(Map<String, Object> map);
	// 获取批次电流 电压数据
	List<ComponentHourModel> queryDataGroupByBatchNo(Map<String, Object> map);
	// 查询今天的组串采集信息
	List<ComponentHourModel> queryTodayGroupCollectInfo(Map<String, Object> map);
	// 查询今天的批次采集信息
	List<ComponentHourModel> queryTodayBatchNoCollectInfo(Map<String, Object> map);

	// 查询批次采集信息
	List<ComponentHourModel> queryBatchNoCollectInfo(Map<String, Object> map);

	// 查询今天整个电站电气采集表的发电量
	BigDecimal queryTodayKwhFromCollect(Map<String, Object> map);
	// 查询今天一个组串电气采集表的发电量
	BigDecimal queryTodayGroupKwhFromCollect(Map<String, Object> map);
	// 查询组串汇聚表的发电量
	ComponentHourModel queryDayKwhInfoFromGroupCollect(Map<String, Object> map);

	BigDecimal queryDayKwhFromGroupCollect(Map<String, Object> map);

	// 查询天汇聚表的发电量
	BigDecimal queryDayKwhFromPowerCollect(Map<String, Object> map);
	
	// 根据memberId查询电站ID列表
	List<String> queryPowerStationIdsByMemberId(String memberId);
	
	// 根据电站ID列表查询总发电量
	BigDecimal queryTotalKwhByPowerStationIds(List<String> powerStationIds);

	PowerStationModel queryPowerStationInfo(String powerStationId);

	// 查询历史发电量
	BigDecimal queryHistoryKwh(Map<String, Object> map);

	// 查询当天发电量
	BigDecimal queryDayKwhFromComponentEnergy(Map<String, Object> map);

	// 查询单个组件发电量原始数据
	List<ComponentElectricModel> queryComponentKwhByBatchNo(Map<String, Object> map);

}
