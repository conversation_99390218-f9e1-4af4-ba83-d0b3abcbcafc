<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ymx.service.photovoltaic.station.mapper.ComponentDayMapper">
	<!-- 新增按天统计数据 -->
	<insert id="saveComponentDayList" parameterType="HashMap">
		insert into t_app_component_day
		(id ,
		powerStationId ,
		batchNo ,
		componentCount ,
		power ,
		kwh ,
		times ,
		createTime)
		values
		<foreach collection="list" item="item"  index= "index" separator=",">
			(
			#{item.id},
			#{item.powerStationId},
			#{item.batchNo},
			#{item.componentCount},
			#{item.power} ,
			#{item.kwh},
			#{item.times},
			#{item.createTime}
			)
		</foreach>
	</insert>

 <!-- 统计年数据  -->
    <select id="queryComponentByYear" resultType="ComponentDayModel" parameterType="HashMap">
        SELECT powerStationId,SUM(kwh) as kwh,DATE_FORMAT(c.createTime ,'%Y') as createTimeCh from t_powerstation_collect c
        where 1=1
        <if test="null != powerStationId and '' != powerStationId">
           AND c.powerStationId = #{powerStationId}
        </if>
        GROUP by DATE_FORMAT(c.createTime ,'%Y') order by DATE_FORMAT(c.createTime ,'%Y') asc limit 20
    </select>
<!-- 统计月数据  -->
    <select id="queryComponentByMonth" resultType="ComponentDayModel" parameterType="HashMap">
        SELECT powerStationId,SUM(kwh) as kwh,DATE_FORMAT(c.createTime ,'%Y-%m') as createTimeCh from t_powerstation_collect c
        where 1=1
        <if test="null != powerStationId and '' != powerStationId">
           AND c.powerStationId = #{powerStationId}
        </if>
        <if test="selecttime != null and selecttime != ''">
            AND DATE_FORMAT(c.createTime ,'%Y') = #{selecttime}
        </if>
        GROUP by DATE_FORMAT(c.createTime ,'%Y-%m') order by DATE_FORMAT(c.createTime ,'%Y-%m') asc
    </select>
    <!-- 统计天数据  -->
    <select id="queryComponentByDay" resultType="ComponentDayModel" parameterType="HashMap">
        SELECT powerStationId,kwh,DATE_FORMAT(c.createTime ,'%m-%d') as createTimeCh from t_powerstation_collect c
        where 1=1
        <if test="null != powerStationId and '' != powerStationId">
           AND c.powerStationId = #{powerStationId}
        </if>
        <if test="selecttime != null and selecttime != ''">
            AND DATE_FORMAT(c.createTime ,'%Y-%m') = #{selecttime}
        </if>
        order by c.createTime  asc
    </select>

    <select id="queryDataByGroupId" resultType="ComponentHourModel" parameterType="HashMap">
        select batchNo,avgOutputCurrent as outputCurrent,sumOutputVoltage as outputVoltage from
        t_group_collect${tableName} where groupId= #{groupId}
        and batchNo like "${day}%" order by batchNo
    </select>

    <!--按批次查询查询出这个批次的电流 电压值是一个什么样的值-->
    <select id="queryDataGroupByBatchNo" resultType="ComponentHourModel" parameterType="HashMap">
    select batchNo,avg(avgOutputCurrent) as outputCurrent ,sum(sumOutputVoltage) as outputVoltage from
    t_group_collect${tableName} where batchNo like "${day}%" group by batchNo
    </select>

    <select id="queryGroupPowerByBatchNo" resultType="ComponentHourModel" parameterType="HashMap">
        select groupId as id,batchNo,truncate(avgOutputCurrent,1) as outputCurrent ,truncate(sumOutputVoltage,0) as outputVoltage from
        t_group_collect${tableName} where batchNo like "${day}%"
    </select>

    <select id="queryBatchNoCollectInfo" resultType="ComponentHourModel" parameterType="HashMap">
        select DATE_FORMAT(collect_time, '%Y%m%d%H%i%s') as batchNo,truncate(avg(groupOutputCurrent),1) as outputCurrent,truncate(sum(groupOutputVoltage),0) as outputVoltage from(
        select e.collect_time,c.belongsGroupId,avg(e.output_current/1000.0) as groupOutputCurrent ,sum(e.output_voltage/1000.0) as groupOutputVoltage
        from t_app_component c,t_component_collect${tableName} e where c.chipId=e.chip_id
        and e.collect_time>  #{startTime} and <![CDATA[ e.collect_time < #{endTime} ]]> group by e.collect_time,c.belongsGroupId) powerTable group by batchNo
    </select>

    <select id="queryTodayGroupCollectInfo" resultType="ComponentHourModel" parameterType="HashMap">
        select DATE_FORMAT(collect_time, '%Y%m%d%H%i%s') as batchNo,avg(output_current/1000.0) as outputCurrent,sum(output_voltage/1000.0) as outputVoltage
        from t_component_collect${tableName}
        where collect_time>= #{startTime} and <![CDATA[ collect_time < #{endTime} ]]> and
        chip_id in (select chipId from t_app_component c where c.belongsGroupId = #{groupId}) group by batchNo asc
    </select>

    <!--先算出功率再累加得到批次功率  不是先算出批次平均电流 总电压 再相乘得到总功率-->
    <select id="queryTodayBatchNoCollectInfo" resultType="ComponentHourModel" parameterType="HashMap">
        select DATE_FORMAT(collect_time, '%Y%m%d%H%i%s') as batchNo,round(avg(groupOutputCurrent),4) as outputCurrent,round(sum(groupOutputVoltage),4) as outputVoltage from(
        select e.collect_time,c.belongsGroupId,avg(e.output_current/1000.0) as groupOutputCurrent ,sum(e.output_voltage/1000.0) as groupOutputVoltage
        from t_app_component c,t_component_collect${tableName} e where c.chipId=e.chip_id
        and e.collect_time>=  #{startTime} and <![CDATA[ e.collect_time < #{endTime} ]]> group by e.collect_time,c.belongsGroupId) powerTable group by batchNo
    </select>




    <!--查询电气采集表的发电量 -->
    <select id="queryTodayKwhFromCollect" resultType="java.math.BigDecimal" parameterType="HashMap">
        select round(sum(batchNoPower)/60000,4) as kwh from(
        select (avg(e.output_current/1000.0)*sum(e.output_voltage/1000.0)) as batchNoPower
        from t_app_component c,t_component_collect${tableName} e where c.chipId=e.chip_id
        and e.collect_time>  #{startTime} and <![CDATA[ e.collect_time < #{endTime} ]]> group by e.collect_time,c.belongsGroupId) powerTable
    </select>

    <select id="queryTodayGroupKwhFromCollect" resultType="java.math.BigDecimal" parameterType="HashMap">
        select round(sum(batchNoPower)/60000,4) as kwh from(
        select (avg(e.output_current/1000.0)*sum(e.output_voltage/1000.0)) as batchNoPower
        from t_app_component c,t_component_collect${tableName} e where c.chipId=e.chip_id and c.belongsGroupId= #{groupId}
        and e.collect_time>  #{startTime} and <![CDATA[ e.collect_time < #{endTime} ]]> group by e.collect_time) powerTable
    </select>

    <!--查询组串汇聚表的发电量-->
    <select id="queryDayKwhInfoFromGroupCollect" resultType="ComponentHourModel" parameterType="HashMap">
        select max(batchNo) as batchNo,round(sum(avgOutputCurrent*sumOutputVoltage)/60000,4) as kwh from
        t_group_collect${tableName} where batchNo like "${day}%"
        <if test="groupId != null and groupId != ''">
            and groupId = #{groupId}
        </if>
    </select>

    <select id="queryDayKwhFromGroupCollect" resultType="java.math.BigDecimal" parameterType="HashMap">
        select round(sum(avgOutputCurrent*sumOutputVoltage)/60000,4) as kwh from
        t_group_collect${tableName} where batchNo like "${day}%" and groupId = #{groupId}
    </select>

    <select id="queryDayKwhFromPowerCollect" resultType="java.math.BigDecimal" parameterType="HashMap">
        select kwh from t_powerstation_collect where powerStationId= #{powerStationId} and batchNo = #{batchNo}
    </select>

    <!-- 根据memberId查询电站ID列表 -->
    <select id="queryPowerStationIdsByMemberId" resultType="java.lang.String" parameterType="java.lang.String">
        select id from t_app_powerstation where createUserId = #{memberId}
    </select>

    <!-- 根据电站ID列表查询总发电量 -->
    <select id="queryTotalKwhByPowerStationIds" resultType="java.math.BigDecimal" parameterType="java.util.List">
        select COALESCE(sum(kwh), 0) from t_powerstation_collect where powerStationId in
        <foreach collection="list" item="powerStationId" open="(" separator="," close=")">
            #{powerStationId}
        </foreach>
    </select>

    <select id="queryPowerStationInfo" resultType="PowerStationModel" parameterType="java.lang.String">
        select sunuptime,sundowntime,collect_gap as collectGap,kwh_rate as kwhRate from t_app_powerstation where id = #{powerStationId}
    </select>

    <!-- 查询历史发电量 -->
    <select id="queryHistoryKwh" resultType="java.math.BigDecimal" parameterType="HashMap">
        select kwh from t_powerstation_collect where powerStationId = #{powerStationId} and batchNo = #{day}
    </select>

    <!-- 查询当天发电量 -->
    <select id="queryDayKwhFromComponentEnergy" resultType="java.math.BigDecimal" parameterType="HashMap">
        SELECT sum(kwh) from t_component_energy
        where powerStationId=#{powerStationId} and batch_no = #{batch_no}
    </select>

</mapper>