package com.ymx.service.photovoltaic.station.service;

import com.ymx.common.base.config.ConfigConstants;
import com.ymx.common.utils.ConfigUtil;
import com.ymx.common.utils.DateUtils;
import com.ymx.service.photovoltaic.station.mapper.ComponentDayMapper;
import com.ymx.service.photovoltaic.station.mapper.ComponentHourMapper;
import com.ymx.service.photovoltaic.station.model.ComponentDayModel;
import com.ymx.service.photovoltaic.station.model.ComponentElectricModel;
import com.ymx.service.photovoltaic.station.model.ComponentHourModel;
import com.ymx.service.photovoltaic.station.model.PowerStationModel;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
public class ComponentDayServiceImpl implements ComponentDayService {
    
	 @Resource
	 private ComponentDayMapper componentDayMapper;
	 @Resource
	 private ComponentHourMapper componentHourMapper;

	private static final LocalTime dayGatherEndTime = LocalTime.parse(ConfigConstants.getConfig("DAY_KWH_GATHER_END_TIME"));

	@Override
	public List<ComponentHourModel> queryGroupPowerByBatchNo(Map<String, Object> map) {
		return componentDayMapper.queryGroupPowerByBatchNo(map);
	}

	@Override
	public List<ComponentDayModel> queryComponentByYear(Map<String, Object> map) {
		return componentDayMapper.queryComponentByYear(map);
	}
	@Override
	public List<ComponentDayModel> queryComponentByMonth(Map<String, Object> map) {
		return componentDayMapper.queryComponentByMonth(map);
	}
	@Override
	public List<ComponentDayModel> queryComponentByDay(Map<String, Object> map) {
		return componentDayMapper.queryComponentByDay(map);
	}
	@Override
	public List<ComponentHourModel> queryComponentByXs(Map<String, Object> map) {
		return componentHourMapper.queryComponentByXs(map);
	}

	@Override
	public List<ComponentHourModel> queryDataByGroupId(Map<String, Object> map) {
		return componentDayMapper.queryDataByGroupId(map);
	}

	@Override
	public List<ComponentHourModel> queryDataGroupByBatchNo(Map<String, Object> map) {
		return componentDayMapper.queryDataGroupByBatchNo(map);
	}

	@Override
	public List<ComponentHourModel> queryTodayGroupCollectInfo(Map<String, Object> map) {
		return componentDayMapper.queryTodayGroupCollectInfo(map);
	}

	@Override
	public List<ComponentHourModel> queryTodayBatchNoCollectInfo(Map<String, Object> map) {
		return componentDayMapper.queryTodayBatchNoCollectInfo(map);
	}

	@Override
	public List<ComponentHourModel> queryBatchNoCollectInfo(Map<String, Object> map) {
		return componentDayMapper.queryBatchNoCollectInfo(map);
	}


	@Override
	public BigDecimal queryTodayKwh(String powerStationId,String sunUpTimeStr,String groupId) {

		LocalTime now = LocalTime.now();
		LocalTime sunUpTime = LocalTime.parse(sunUpTimeStr);

		// 当前时间小于采集开始时间 直接返回0
		if (now.isBefore(sunUpTime)) {
			return BigDecimal.valueOf(0);
		}

		BigDecimal collectKwh;
		// 大于汇聚结束时间 直接查天汇聚表
		if(now.isAfter(dayGatherEndTime))
		{

			String today = LocalDate.now().toString();
			String day = today.replace("-", "");
			HashMap<String, Object> map = new HashMap<>();
			if (groupId != null) {
				map.put("tableName", "_" + powerStationId);
				map.put("day", day);
				map.put("groupId", groupId);
				// 查组串汇聚表
				collectKwh = componentDayMapper.queryDayKwhFromGroupCollect(map);
			} else {
				map.put("powerStationId", powerStationId);
				map.put("batchNo", day);
				// 查电站汇聚表
				collectKwh = componentDayMapper.queryDayKwhFromPowerCollect(map);
			}
			return collectKwh;
		}

		// 大于采集开始时间 小于汇聚结束时间 先查组串汇聚表 再查采集表
		collectKwh=getMiddleDayKwh(groupId,powerStationId,sunUpTimeStr,now);

		return collectKwh;
	}

	// 大于采集开始时间 小于汇聚结束时间 先查组串汇聚表 再查采集表
	private BigDecimal getMiddleDayKwh(String groupId,String powerStationId,String sunUpTimeStr,LocalTime now)
	{
		HashMap<String, Object> map = new HashMap<>();
		BigDecimal collectKwh;
		String today = LocalDate.now().toString();
		String day = today.replace("-", "");
		// 先查组串汇聚表 再查采集表
		map.put("tableName", "_" + powerStationId);
		map.put("day", day);
		if (groupId != null) {
			map.put("groupId", groupId);
		}
		// 先查组串汇聚表
		ComponentHourModel comHourModel = componentDayMapper.queryDayKwhInfoFromGroupCollect(map);
		if (comHourModel != null && comHourModel.getBatchNo() != null) {
			map.put("startTime", DateUtils.getCreateTimeCh(comHourModel.getBatchNo()));
		} else {
			map.put("startTime", today + " " + sunUpTimeStr);
		}
		map.put("endTime", today + " " + now.withNano(0));
		int timeStep = ConfigUtil.getTimeStep();
		map.put("timeStep", timeStep);
		// 再查采集表
		if (groupId != null) {
			collectKwh = componentDayMapper.queryTodayGroupKwhFromCollect(map);
		} else {
			collectKwh = componentDayMapper.queryTodayKwhFromCollect(map);
		}
		// 如果批次汇聚表和采集表都不为null 就把两个结果相加 否则只返批次汇聚表或采集表一个结果
		if (comHourModel != null && comHourModel.getKwh() != null) {
			collectKwh=collectKwh!=null?collectKwh.add(comHourModel.getKwh()):comHourModel.getKwh();
		}
		return collectKwh;
	}

	@Override
	public BigDecimal queryOldDayKwh(String powerStationId, String batchNo,String groupId) {
		HashMap<String, Object> map = new HashMap<>();
		BigDecimal dayKwh;
		if (groupId != null) {
			map.put("tableName", "_" + powerStationId);
			map.put("day", batchNo);
			map.put("groupId", groupId);
			dayKwh = componentDayMapper.queryDayKwhFromGroupCollect(map);;
		} else {
			map.put("powerStationId", powerStationId);
			map.put("batchNo", batchNo);
			dayKwh = componentDayMapper.queryDayKwhFromPowerCollect(map);
		}
		return dayKwh;
	}

	@Override
	public Map<String, Object> calculateContribution(String memberId) {
		Map<String, Object> result = new HashMap<>();
		
		// 根据memberId查询电站ID列表
		List<String> powerStationIds = componentDayMapper.queryPowerStationIdsByMemberId(memberId);
		
		if (powerStationIds == null || powerStationIds.isEmpty()) {
			// 如果没有找到电站，返回0值
			result.put("totalKwh", BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
			result.put("co2Reduction", BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
			result.put("coalSaving", BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
			result.put("treePlanting", BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
			return result;
		}
		
		// 根据电站ID列表查询总发电量
		BigDecimal totalKwh = componentDayMapper.queryTotalKwhByPowerStationIds(powerStationIds);
		if (totalKwh == null) {
			totalKwh = BigDecimal.ZERO;
		}
		
		// 计算社会贡献指标
		// 减排CO2总量 = 总的kwh * 0.997
		BigDecimal co2Reduction = totalKwh.multiply(new BigDecimal("0.997")).setScale(2, RoundingMode.HALF_UP);
		
		// 节约标准煤总量 = 总的kwh * 0.404
		BigDecimal coalSaving = totalKwh.multiply(new BigDecimal("0.404")).setScale(2, RoundingMode.HALF_UP);
		
		// 等效植树值 = 总的kwh * 0.054
		BigDecimal treePlanting = totalKwh.multiply(new BigDecimal("0.054")).setScale(2, RoundingMode.HALF_UP);
		
		result.put("totalKwh", totalKwh.setScale(2, RoundingMode.HALF_UP));
		result.put("co2Reduction", co2Reduction);
		result.put("coalSaving", coalSaving);
		result.put("treePlanting", treePlanting);
		
		return result;
	}

	@Override
	public PowerStationModel queryPowerStationInfo(String powerStationId) {
		return componentDayMapper.queryPowerStationInfo(powerStationId);
	}

	@Override
	public BigDecimal queryHistoryKwh(String powerStationId, String day) {
		HashMap<String, Object> map = new HashMap<>();
		map.put("powerStationId", powerStationId);
		map.put("day", day);
		return componentDayMapper.queryHistoryKwh(map);
	}

	@Override
	public BigDecimal queryDayKwhFromComponentEnergy(Map<String, Object> map) {
		return componentDayMapper.queryDayKwhFromComponentEnergy(map);
	}

	@Override
	public List<ComponentElectricModel> queryComponentKwhByBatchNo(Map<String, Object> map) {
		return componentDayMapper.queryComponentKwhByBatchNo(map);
	}

}
