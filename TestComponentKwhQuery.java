package com.ymx.test;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.ymx.service.photovoltaic.station.model.ComponentElectricModel;
import com.ymx.service.photovoltaic.station.service.ComponentDayService;

/**
 * 测试新增的组件发电量查询功能
 */
@RunWith(SpringJUnit4ClassRunner.class)
public class TestComponentKwhQuery {

    private ComponentDayService componentDayService;

    @Test
    public void testQueryComponentKwhByBatchNo() {
        // 测试参数
        String powerStationId = "test_power_station_id";
        String batchNo = "20231201";
        
        Map<String, Object> kwhMap = new HashMap<>();
        kwhMap.put("powerStationId", powerStationId);
        kwhMap.put("batch_no", batchNo);

        // 调用新增的方法
        List<ComponentElectricModel> componentKwhList = componentDayService.queryComponentKwhByBatchNo(kwhMap);
        
        // 计算原始kwh总和
        BigDecimal originalSumKwh = BigDecimal.ZERO;
        if (componentKwhList != null && !componentKwhList.isEmpty()) {
            for (ComponentElectricModel component : componentKwhList) {
                if (component.getKwh() != null) {
                    originalSumKwh = originalSumKwh.add(BigDecimal.valueOf(component.getKwh()));
                }
            }
        }

        // 模拟kwhRate调整
        BigDecimal kwhRate = new BigDecimal("105.5"); // 假设调整率为105.5%
        BigDecimal adjustedKwh = BigDecimal.ZERO;
        
        if (originalSumKwh.compareTo(BigDecimal.ZERO) > 0 && kwhRate != null) {
            // 计算调整后的发电量：原始发电量 * kwhRate / 100
            adjustedKwh = originalSumKwh.multiply(kwhRate).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
            System.out.println("原始发电量: " + originalSumKwh + ", kwhRate: " + kwhRate + ", 调整后发电量: " + adjustedKwh);
        } else {
            adjustedKwh = originalSumKwh;
            System.out.println("未进行kwh调整 - originalSumKwh: " + originalSumKwh + ", kwhRate: " + kwhRate);
        }

        // 验证结果
        assert componentKwhList != null : "查询结果不应为null";
        assert adjustedKwh.compareTo(BigDecimal.ZERO) >= 0 : "调整后的发电量应该大于等于0";
        
        System.out.println("测试通过：查询到 " + (componentKwhList != null ? componentKwhList.size() : 0) + " 个组件的发电量数据");
        System.out.println("最终计算的todayKwh: " + adjustedKwh);
    }

    /**
     * 测试SQL查询语句的正确性
     */
    @Test
    public void testSqlQuery() {
        System.out.println("新增的SQL查询语句：");
        System.out.println("select c.chipId, e.kwh as kwh");
        System.out.println("from t_app_component c");
        System.out.println("left join t_component_energy e on c.chipId=e.chip_id and e.batch_no=#{batch_no}");
        System.out.println("where c.powerStationId=#{powerStationId}");
        System.out.println();
        System.out.println("该查询将返回指定电站和批次的所有组件及其发电量数据");
        System.out.println("如果某个组件在指定批次没有发电量数据，kwh字段将为null");
    }

    /**
     * 验证与ComponentV1AppController逻辑的一致性
     */
    @Test
    public void testConsistencyWithComponentV1Controller() {
        // 模拟数据
        BigDecimal originalSumKwh = new BigDecimal("100.50");
        BigDecimal kwhRate = new BigDecimal("105.5");
        
        // StatisticsAppController中的计算逻辑
        BigDecimal todayKwh = originalSumKwh.multiply(kwhRate).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
        
        // ComponentV1AppController中的计算逻辑
        BigDecimal newTotalKwh = originalSumKwh.multiply(kwhRate).divide(BigDecimal.valueOf(100), RoundingMode.HALF_UP).setScale(2, RoundingMode.HALF_UP);
        
        System.out.println("StatisticsAppController计算结果: " + todayKwh);
        System.out.println("ComponentV1AppController计算结果: " + newTotalKwh);
        
        // 验证两个结果是否一致
        assert todayKwh.compareTo(newTotalKwh) == 0 : "两个Controller的计算结果应该一致";
        
        System.out.println("一致性测试通过：两个Controller使用相同的计算逻辑");
    }
}
