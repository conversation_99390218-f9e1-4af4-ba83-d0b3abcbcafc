package com.ymx.test;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.ymx.service.photovoltaic.station.model.ComponentElectricModel;
import com.ymx.service.photovoltaic.station.service.ComponentDayService;

/**
 * 测试新增的组件发电量查询功能
 */
@RunWith(SpringJUnit4ClassRunner.class)
public class TestComponentKwhQuery {

    private ComponentDayService componentDayService;

    @Test
    public void testQueryComponentKwhByBatchNo() {
        // 测试参数
        String powerStationId = "test_power_station_id";
        String batchNo = "20231201";
        
        Map<String, Object> kwhMap = new HashMap<>();
        kwhMap.put("powerStationId", powerStationId);
        kwhMap.put("batch_no", batchNo);

        // 调用新增的方法
        List<ComponentElectricModel> componentKwhList = componentDayService.queryComponentKwhByBatchNo(kwhMap);
        
        // 计算原始kwh总和
        BigDecimal originalSumKwh = BigDecimal.ZERO;
        if (componentKwhList != null && !componentKwhList.isEmpty()) {
            for (ComponentElectricModel component : componentKwhList) {
                if (component.getKwh() != null) {
                    originalSumKwh = originalSumKwh.add(BigDecimal.valueOf(component.getKwh()));
                }
            }
        }

        // 模拟kwhRate调整
        BigDecimal kwhRate = new BigDecimal("105.5"); // 假设调整率为105.5%
        BigDecimal adjustedKwh = BigDecimal.ZERO;
        
        if (originalSumKwh.compareTo(BigDecimal.ZERO) > 0 && kwhRate != null) {
            // 计算调整后的发电量：原始发电量 * kwhRate / 100
            adjustedKwh = originalSumKwh.multiply(kwhRate).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
            System.out.println("原始发电量: " + originalSumKwh + ", kwhRate: " + kwhRate + ", 调整后发电量: " + adjustedKwh);
        } else {
            adjustedKwh = originalSumKwh;
            System.out.println("未进行kwh调整 - originalSumKwh: " + originalSumKwh + ", kwhRate: " + kwhRate);
        }

        // 验证结果
        assert componentKwhList != null : "查询结果不应为null";
        assert adjustedKwh.compareTo(BigDecimal.ZERO) >= 0 : "调整后的发电量应该大于等于0";
        
        System.out.println("测试通过：查询到 " + (componentKwhList != null ? componentKwhList.size() : 0) + " 个组件的发电量数据");
        System.out.println("最终计算的todayKwh: " + adjustedKwh);
    }

    /**
     * 测试SQL查询语句的正确性
     */
    @Test
    public void testSqlQuery() {
        System.out.println("新增的SQL查询语句：");
        System.out.println("select c.chipId, e.kwh as kwh");
        System.out.println("from t_app_component c");
        System.out.println("left join t_component_energy e on c.chipId=e.chip_id and e.batch_no=#{batch_no}");
        System.out.println("where c.powerStationId=#{powerStationId}");
        System.out.println();
        System.out.println("该查询将返回指定电站和批次的所有组件及其发电量数据");
        System.out.println("如果某个组件在指定批次没有发电量数据，kwh字段将为null");
    }

    /**
     * 验证与ComponentV1AppController逻辑的一致性
     */
    @Test
    public void testConsistencyWithComponentV1Controller() {
        // 模拟组件数据
        List<ComponentElectricModel> componentList = new ArrayList<>();
        ComponentElectricModel comp1 = new ComponentElectricModel();
        comp1.setChipId("chip001");
        comp1.setKwh(50.25);
        componentList.add(comp1);

        ComponentElectricModel comp2 = new ComponentElectricModel();
        comp2.setChipId("chip002");
        comp2.setKwh(30.15);
        componentList.add(comp2);

        ComponentElectricModel comp3 = new ComponentElectricModel();
        comp3.setChipId("chip003");
        comp3.setKwh(20.10);
        componentList.add(comp3);

        // 计算原始总和
        BigDecimal originalSumKwh = BigDecimal.ZERO;
        for (ComponentElectricModel component : componentList) {
            if (component.getKwh() != null) {
                originalSumKwh = originalSumKwh.add(BigDecimal.valueOf(component.getKwh()));
            }
        }

        BigDecimal kwhRate = new BigDecimal("105.5");

        // 模拟StatisticsAppController中的新逻辑
        // 第二步：计算新的总发电量
        BigDecimal newTotalKwh = originalSumKwh.multiply(kwhRate).divide(BigDecimal.valueOf(100), RoundingMode.HALF_UP).setScale(2, RoundingMode.HALF_UP);

        // 第三步：按比例重新分配每个ComponentElectricModel的kwh
        for (ComponentElectricModel component : componentList) {
            if (component.getKwh() != null) {
                BigDecimal originalKwh = BigDecimal.valueOf(component.getKwh());
                // 计算比例：原始kwh / 原始总和
                BigDecimal ratio = originalKwh.divide(originalSumKwh, 6, RoundingMode.HALF_UP);
                // 新kwh = 新总发电量 * 比例
                BigDecimal newKwh = newTotalKwh.multiply(ratio).setScale(2, RoundingMode.HALF_UP);
                component.setKwh(newKwh.doubleValue());
            }
        }

        // 第四步：使用新的kwh汇总，得到todayKwh的值（等同于adjustedSumKwh）
        BigDecimal todayKwh = BigDecimal.ZERO;
        for (ComponentElectricModel component : componentList) {
            if (component.getKwh() != null) {
                todayKwh = todayKwh.add(BigDecimal.valueOf(component.getKwh()));
            }
        }
        todayKwh = todayKwh.setScale(2, RoundingMode.HALF_UP);

        System.out.println("原始kwh总和: " + originalSumKwh);
        System.out.println("kwhRate: " + kwhRate);
        System.out.println("新总发电量(newTotalKwh): " + newTotalKwh);
        System.out.println("重新分配后汇总(todayKwh): " + todayKwh);

        // 验证todayKwh应该等于newTotalKwh（在精度范围内）
        BigDecimal difference = todayKwh.subtract(newTotalKwh).abs();
        assert difference.compareTo(new BigDecimal("0.01")) <= 0 : "todayKwh应该与newTotalKwh基本相等";

        System.out.println("一致性测试通过：StatisticsAppController现在使用与ComponentV1AppController相同的按比例分配逻辑");
        System.out.println("差值: " + difference);
    }
}
