# 修改总结

## 已完成的修改

### 1. 在 ComponentDayMapper.xml 中新增 SQL 查询

在 `photovoltaic-service/src/main/java/com/ymx/service/photovoltaic/station/mapper/ComponentDayMapper.xml` 中新增了以下 SQL：

```xml
<!-- 查询单个组件发电量原始数据 -->
<select id="queryComponentKwhByBatchNo" resultType="ComponentElectricModel" parameterType="HashMap">
    select c.chipId, e.kwh as kwh
    from t_app_component c
    left join t_component_energy e on c.chipId=e.chip_id and e.batch_no=#{batch_no}
    where c.powerStationId=#{powerStationId}
</select>
```

### 2. 在 ComponentDayMapper 接口中新增方法

在 `photovoltaic-service/src/main/java/com/ymx/service/photovoltaic/station/mapper/ComponentDayMapper.java` 中新增：

```java
// 查询单个组件发电量原始数据
List<ComponentElectricModel> queryComponentKwhByBatchNo(Map<String, Object> map);
```

### 3. 在 ComponentDayService 接口中新增方法

在 `photovoltaic-service/src/main/java/com/ymx/service/photovoltaic/station/service/ComponentDayService.java` 中新增：

```java
// 查询单个组件发电量原始数据
List<ComponentElectricModel> queryComponentKwhByBatchNo(Map<String, Object> map);
```

### 4. 在 ComponentDayServiceImpl 中实现新方法

在 `photovoltaic-service/src/main/java/com/ymx/service/photovoltaic/station/service/ComponentDayServiceImpl.java` 中实现：

```java
@Override
public List<ComponentElectricModel> queryComponentKwhByBatchNo(Map<String, Object> map) {
    return componentDayMapper.queryComponentKwhByBatchNo(map);
}
```

### 5. 修改 StatisticsAppController 中的 queryComponentByStatistics 方法

在 `photovoltaic-app/src/main/java/com/ymx/app/controller/station/StatisticsAppController.java` 中：

1. **使用新的 SQL 查询获取单个组件发电量原始数据**：
   ```java
   // 获取单个组件发电量的原始数据
   List<ComponentElectricModel> componentKwhList = componentDayService.queryComponentKwhByBatchNo(kwhMap);
   ```

2. **计算原始 kwh 总和**：
   ```java
   // 计算原始kwh总和
   BigDecimal originalSumKwh = BigDecimal.ZERO;
   if (componentKwhList != null && !componentKwhList.isEmpty()) {
       for (ComponentElectricModel component : componentKwhList) {
           if (component.getKwh() != null) {
               originalSumKwh = originalSumKwh.add(BigDecimal.valueOf(component.getKwh()));
           }
       }
   }
   ```

3. **参考 ComponentV1AppController.getComponentViewList 中的逻辑进行处理**：
   ```java
   // 参考ComponentV1AppController.getComponentViewList中的逻辑进行处理
   if (originalSumKwh.compareTo(BigDecimal.ZERO) > 0 && powerStationModel.getKwhRate() != null) {
       BigDecimal kwhRate = powerStationModel.getKwhRate();
       // 计算调整后的发电量：原始发电量 * kwhRate / 100
       todayKwh = originalSumKwh.multiply(kwhRate).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
       logger.info("原始发电量: {}, kwhRate: {}, 调整后发电量: {}", originalSumKwh, kwhRate, todayKwh);
   } else {
       todayKwh = originalSumKwh;
       logger.info("未进行kwh调整 - originalSumKwh: {}, kwhRate: {}", originalSumKwh, 
               powerStationModel.getKwhRate());
   }
   ```

## 修改的核心逻辑（最新版本）

1. **获取原始数据**：通过新的 SQL 查询 `queryComponentKwhByBatchNo` 获取每个组件的发电量原始数据
2. **计算原始总和**：将所有组件的原始发电量相加得到 `originalSumKwh`
3. **计算新的总发电量**：`newTotalKwh = originalSumKwh * kwhRate / 100`
4. **按比例重新分配**：为每个组件重新计算 kwh 值
   - 计算比例：`ratio = 原始kwh / 原始总和`
   - 新kwh：`newKwh = newTotalKwh * ratio`
5. **重新汇总**：将所有组件的新 kwh 值相加得到最终的 `todayKwh`

## 详细的计算步骤

### 第一步：获取原始数据
```java
List<ComponentElectricModel> componentKwhList = componentDayService.queryComponentKwhByBatchNo(kwhMap);
```

### 第二步：计算原始总和
```java
BigDecimal originalSumKwh = BigDecimal.ZERO;
for (ComponentElectricModel component : componentKwhList) {
    if (component.getKwh() != null) {
        originalSumKwh = originalSumKwh.add(BigDecimal.valueOf(component.getKwh()));
    }
}
```

### 第三步：计算新的总发电量
```java
BigDecimal newTotalKwh = originalSumKwh.multiply(kwhRate).divide(BigDecimal.valueOf(100), RoundingMode.HALF_UP).setScale(2, RoundingMode.HALF_UP);
```

### 第四步：按比例重新分配每个组件的kwh
```java
for (ComponentElectricModel component : componentKwhList) {
    if (component.getKwh() != null) {
        BigDecimal originalKwh = BigDecimal.valueOf(component.getKwh());
        BigDecimal ratio = originalKwh.divide(originalSumKwh, 6, RoundingMode.HALF_UP);
        BigDecimal newKwh = newTotalKwh.multiply(ratio).setScale(2, RoundingMode.HALF_UP);
        component.setKwh(newKwh.doubleValue());
    }
}
```

### 第五步：重新汇总得到最终的todayKwh
```java
todayKwh = BigDecimal.ZERO;
for (ComponentElectricModel component : componentKwhList) {
    if (component.getKwh() != null) {
        todayKwh = todayKwh.add(BigDecimal.valueOf(component.getKwh()));
    }
}
todayKwh = todayKwh.setScale(2, RoundingMode.HALF_UP);
```

## 预期效果

修改后，StatisticsAppController 中的 `queryComponentByStatistics` 方法将：
- 使用新的 SQL 查询获取单个组件发电量的原始数据
- 完全按照 ComponentV1AppController.getComponentViewList 中的四步逻辑处理数据
- 计算出与 `adjustedSumKwh` 完全相等的 `todayKwh`

## 关键差异说明

- **之前的实现**：`todayKwh = newTotalKwh`（理论计算值）
- **现在的实现**：`todayKwh = adjustedSumKwh`（按比例重新分配后的实际汇总值）

这样确保了 StatisticsAppController 和 ComponentV1AppController 使用完全相同的计算逻辑和精度处理方式。
