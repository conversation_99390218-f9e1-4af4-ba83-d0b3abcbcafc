# 修改总结

## 已完成的修改

### 1. 在 ComponentDayMapper.xml 中新增 SQL 查询

在 `photovoltaic-service/src/main/java/com/ymx/service/photovoltaic/station/mapper/ComponentDayMapper.xml` 中新增了以下 SQL：

```xml
<!-- 查询单个组件发电量原始数据 -->
<select id="queryComponentKwhByBatchNo" resultType="ComponentElectricModel" parameterType="HashMap">
    select c.chipId, e.kwh as kwh
    from t_app_component c
    left join t_component_energy e on c.chipId=e.chip_id and e.batch_no=#{batch_no}
    where c.powerStationId=#{powerStationId}
</select>
```

### 2. 在 ComponentDayMapper 接口中新增方法

在 `photovoltaic-service/src/main/java/com/ymx/service/photovoltaic/station/mapper/ComponentDayMapper.java` 中新增：

```java
// 查询单个组件发电量原始数据
List<ComponentElectricModel> queryComponentKwhByBatchNo(Map<String, Object> map);
```

### 3. 在 ComponentDayService 接口中新增方法

在 `photovoltaic-service/src/main/java/com/ymx/service/photovoltaic/station/service/ComponentDayService.java` 中新增：

```java
// 查询单个组件发电量原始数据
List<ComponentElectricModel> queryComponentKwhByBatchNo(Map<String, Object> map);
```

### 4. 在 ComponentDayServiceImpl 中实现新方法

在 `photovoltaic-service/src/main/java/com/ymx/service/photovoltaic/station/service/ComponentDayServiceImpl.java` 中实现：

```java
@Override
public List<ComponentElectricModel> queryComponentKwhByBatchNo(Map<String, Object> map) {
    return componentDayMapper.queryComponentKwhByBatchNo(map);
}
```

### 5. 修改 StatisticsAppController 中的 queryComponentByStatistics 方法

在 `photovoltaic-app/src/main/java/com/ymx/app/controller/station/StatisticsAppController.java` 中：

1. **使用新的 SQL 查询获取单个组件发电量原始数据**：
   ```java
   // 获取单个组件发电量的原始数据
   List<ComponentElectricModel> componentKwhList = componentDayService.queryComponentKwhByBatchNo(kwhMap);
   ```

2. **计算原始 kwh 总和**：
   ```java
   // 计算原始kwh总和
   BigDecimal originalSumKwh = BigDecimal.ZERO;
   if (componentKwhList != null && !componentKwhList.isEmpty()) {
       for (ComponentElectricModel component : componentKwhList) {
           if (component.getKwh() != null) {
               originalSumKwh = originalSumKwh.add(BigDecimal.valueOf(component.getKwh()));
           }
       }
   }
   ```

3. **参考 ComponentV1AppController.getComponentViewList 中的逻辑进行处理**：
   ```java
   // 参考ComponentV1AppController.getComponentViewList中的逻辑进行处理
   if (originalSumKwh.compareTo(BigDecimal.ZERO) > 0 && powerStationModel.getKwhRate() != null) {
       BigDecimal kwhRate = powerStationModel.getKwhRate();
       // 计算调整后的发电量：原始发电量 * kwhRate / 100
       todayKwh = originalSumKwh.multiply(kwhRate).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
       logger.info("原始发电量: {}, kwhRate: {}, 调整后发电量: {}", originalSumKwh, kwhRate, todayKwh);
   } else {
       todayKwh = originalSumKwh;
       logger.info("未进行kwh调整 - originalSumKwh: {}, kwhRate: {}", originalSumKwh, 
               powerStationModel.getKwhRate());
   }
   ```

## 修改的核心逻辑

1. **获取原始数据**：通过新的 SQL 查询 `queryComponentKwhByBatchNo` 获取每个组件的发电量原始数据
2. **计算总和**：将所有组件的原始发电量相加得到 `originalSumKwh`
3. **应用调整率**：如果存在 `kwhRate`，则按照 ComponentV1AppController 中的逻辑进行调整：`调整后发电量 = 原始发电量 * kwhRate / 100`
4. **保持一致性**：确保计算出的 `todayKwh` 与 ComponentV1AppController 中的 `adjustedSumKwh` 计算逻辑完全一致

## 预期效果

修改后，StatisticsAppController 中的 `queryComponentByStatistics` 方法将：
- 使用新的 SQL 查询获取单个组件发电量的原始数据
- 按照与 ComponentV1AppController.getComponentViewList 相同的逻辑处理数据
- 计算出与 `adjustedSumKwh` 相等的 `todayKwh`
